# SEVALLA ENVIRONMENT VARIABLES - OAUTH-ONLY MODE
# Minimal configuration for Discord OAuth authentication without bot functionality

# =============================================================================
# CRITICAL VARIABLES (Required for OAuth-only mode)
# =============================================================================

# Discord OAuth Configuration (Required for user authentication)
BOT_CLIENT_ID=1394521471862308884
BOT_CLIENT_SECRET=1IOBGoqWI8s4DxeX4SgF6G3dLH7vsAXD
DISCORD_CLIENT_ID=1394521471862308884
DISCORD_CLIENT_SECRET=1IOBGoqWI8s4DxeX4SgF6G3dLH7vsAXD

# Application Configuration
NODE_ENV=production
PORT=8080

# Database Configuration
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# Security Keys (Required for session management)
USER_ENCRYPTION_KEY=5b294018a4cccfd435083d865b5199ef4d259f936d04dd8f35fd074d8ea99cbb
SESSION_ENCRYPTION_KEY=833ff390d1c4413266ee115259b642874c0d8517214cb05b45b2b87da9d48d03
CSRF_ENCRYPTION_KEY=5e9d3e69535613f9e7d797a54b5df9207a1c95d8cd05c297eee3a6cd15541ed9

# =============================================================================
# APPLICATION URLS
# =============================================================================

WEB_URL=https://discordbot-energex-jkhvk.sevalla.app
APP_URL=https://discordbot-energex-jkhvk.sevalla.app
NEXT_PUBLIC_API_ENDPOINT=https://discordbot-energex-backend-nqzv2.sevalla.app
INTERNAL_API_ENDPOINT=https://discordbot-energex-backend-nqzv2.sevalla.app

# =============================================================================
# OPTIONAL SETTINGS
# =============================================================================

ENABLE_ENV_LOGIN=false
SESSION_ISOLATION_ENABLED=true
SESSION_FINGERPRINTING_ENABLED=true
AUTOMATIC_TOKEN_ROTATION=true

# AI Configuration (Optional)
ANTHROPIC_API_KEY=************************************************************************************************************
USE_MASTRA=true
DEFAULT_AI_CHANNEL=general

# =============================================================================
# INSTRUCTIONS FOR OAUTH-ONLY MODE:
# =============================================================================
# 1. Go to your Sevalla dashboard
# 2. Navigate to your backend project: discordbot-energex-backend-nqzv2
# 3. Add the variables above (DO NOT include DISCORD_TOKEN)
# 4. Save configuration and redeploy
# 5. Application will start in OAuth-only mode
# 6. Users can authenticate via Discord OAuth at /api/auth/login
# 7. Discord bot features (slash commands, events) will be disabled
#
# NOTE: This mode provides Discord user authentication without bot functionality.
# To enable bot features later, add DISCORD_TOKEN to environment variables.
