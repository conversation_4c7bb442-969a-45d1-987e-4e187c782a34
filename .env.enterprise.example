# =============================================================================
# ENTERPRISE DISCORD BOT CONFIGURATION
# =============================================================================
# Copy this file to .env.local and configure for your environment

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=production
PORT=8080
HOST=0.0.0.0

# Application URLs
APP_URL=https://your-domain.com
WEB_URL=https://your-dashboard.com
INTERNAL_API_ENDPOINT=https://your-api.com

# =============================================================================
# DISCORD CONFIGURATION
# =============================================================================
# Discord Bot Token (Optional - for bot functionality)
DISCORD_TOKEN=your_discord_bot_token_here

# Discord OAuth (Required for user authentication)
BOT_CLIENT_ID=your_discord_client_id
BOT_CLIENT_SECRET=your_discord_client_secret
DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=****************************************/database?sslmode=require

# Database Connection Pool Settings
DB_POOL_MAX=20
DB_POOL_MIN=2
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=5000
DB_ACQUIRE_TIMEOUT=10000
DB_QUERY_TIMEOUT=30000
DB_STATEMENT_TIMEOUT=60000
DB_APPLICATION_NAME=energex-discord-bot

# =============================================================================
# REDIS CONFIGURATION (Optional - for caching and rate limiting)
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0
REDIS_KEY_PREFIX=discord_bot:

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Encryption Keys (Generate with: openssl rand -hex 32)
USER_ENCRYPTION_KEY=your_64_character_hex_key_here
SESSION_ENCRYPTION_KEY=your_64_character_hex_key_here
CSRF_ENCRYPTION_KEY=your_64_character_hex_key_here

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h
JWT_ISSUER=discord-bot-energex
JWT_AUDIENCE=discord-bot-energex

# Session Configuration
SESSION_ISOLATION_ENABLED=true
SESSION_FINGERPRINTING_ENABLED=true
AUTOMATIC_TOKEN_ROTATION=true
SESSION_TIMEOUT=86400000

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
RATE_LIMIT_TTL=60
RATE_LIMIT_MAX=100
RATE_LIMIT_SKIP_SUCCESSFUL=false
RATE_LIMIT_SKIP_FAILED=false

# API Rate Limits (per hour)
API_RATE_LIMIT_FREE=1000
API_RATE_LIMIT_STARTER=10000
API_RATE_LIMIT_PROFESSIONAL=100000
API_RATE_LIMIT_ENTERPRISE=-1

# =============================================================================
# CACHING CONFIGURATION
# =============================================================================
CACHE_TTL=300
CACHE_MAX_ITEMS=1000
CACHE_PREFIX=discord_bot

# Cache Strategy
CACHE_STRATEGY=lru
ENABLE_CACHE_COMPRESSION=true
CACHE_NAMESPACE_ISOLATION=true

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
# Logging
LOG_LEVEL=info
ENABLE_STRUCTURED_LOGGING=true
ENABLE_AUDIT_LOGGING=true
LOG_RETENTION_DAYS=90

# Metrics
ENABLE_PROMETHEUS_METRICS=true
METRICS_PORT=9090
METRICS_PATH=/metrics

# Health Checks
HEALTH_CHECK_TIMEOUT=5000
MEMORY_THRESHOLD=512
DISK_THRESHOLD=80

# External Health Checks
EXTERNAL_HEALTH_CHECKS=https://discord.com/api/v10/gateway

# =============================================================================
# MULTI-TENANCY CONFIGURATION
# =============================================================================
# Organization Settings
DEFAULT_ORGANIZATION_TIER=free
ENABLE_ORGANIZATION_ISOLATION=true
ENABLE_RESOURCE_QUOTAS=true

# Billing Integration
ENABLE_BILLING=false
BILLING_PROVIDER=stripe
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# =============================================================================
# AI CONFIGURATION
# =============================================================================
# Anthropic
ANTHROPIC_API_KEY=your_anthropic_api_key

# OpenAI
OPENAI_API_KEY=your_openai_api_key

# Google AI
GOOGLE_GENERATIVE_AI_API_KEY=your_google_ai_api_key

# Mastra Configuration
USE_MASTRA=true
DEFAULT_AI_CHANNEL=general

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_FEATURE_FLAGS=true
FEATURE_FLAG_CACHE_TTL=300

# Default Feature Flags
FEATURE_ADVANCED_ANALYTICS=false
FEATURE_CUSTOM_BRANDING=false
FEATURE_API_ACCESS=true
FEATURE_WEBHOOKS=false
FEATURE_SSO=false

# =============================================================================
# SECURITY HEADERS & CORS
# =============================================================================
# CORS Configuration
CORS_ORIGIN=https://your-dashboard.com,https://your-domain.com
CORS_CREDENTIALS=true
CORS_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With

# Security Headers
ENABLE_HELMET=true
ENABLE_HSTS=true
HSTS_MAX_AGE=31536000
ENABLE_CSP=true
CSP_DIRECTIVES=default-src 'self'; script-src 'self' 'unsafe-inline'

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Request Timeouts
REQUEST_TIMEOUT=30000
KEEP_ALIVE_TIMEOUT=5000

# Compression
ENABLE_COMPRESSION=true
COMPRESSION_THRESHOLD=1024

# Body Parsing
MAX_JSON_SIZE=10mb
MAX_URL_ENCODED_SIZE=10mb

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
# Email (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Webhook Notifications
WEBHOOK_TIMEOUT=5000
WEBHOOK_RETRY_ATTEMPTS=3
WEBHOOK_RETRY_DELAY=1000

# =============================================================================
# BACKUP & DISASTER RECOVERY
# =============================================================================
# Database Backups
ENABLE_AUTO_BACKUP=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_TYPE=s3

# S3 Configuration (for backups)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-backup-bucket

# =============================================================================
# DEVELOPMENT & DEBUGGING
# =============================================================================
# Development Settings
ENABLE_DEBUG_MODE=false
ENABLE_QUERY_LOGGING=false
ENABLE_PERFORMANCE_PROFILING=false

# API Documentation
ENABLE_SWAGGER=true
SWAGGER_PATH=/api/docs
SWAGGER_TITLE=Discord Bot EnergeX API
SWAGGER_VERSION=1.0.0

# =============================================================================
# WHOP INTEGRATION (Optional)
# =============================================================================
WHOP_API_KEY=your_whop_api_key_here
NEXT_PUBLIC_WHOP_APP_ID=your_whop_app_id_here
NEXT_PUBLIC_WHOP_AGENT_USER_ID=your_whop_agent_user_id_here
NEXT_PUBLIC_WHOP_COMPANY_ID=your_whop_company_id_here

# =============================================================================
# ENVIRONMENT SPECIFIC OVERRIDES
# =============================================================================
# These can be overridden per environment

# Production Overrides
# NODE_ENV=production
# LOG_LEVEL=warn
# ENABLE_DEBUG_MODE=false

# Staging Overrides
# NODE_ENV=staging
# LOG_LEVEL=debug
# ENABLE_DEBUG_MODE=true

# Development Overrides
# NODE_ENV=development
# LOG_LEVEL=debug
# ENABLE_DEBUG_MODE=true
# ENABLE_QUERY_LOGGING=true
