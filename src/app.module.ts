import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { GatewayIntentBits } from 'discord.js';
import { NecordModule } from 'necord';

// Core modules
import { CoreModule } from './core/core.module';
import { DatabaseModule } from './core/database/database.module';
import { SecurityModule } from './core/security/security.module';

// Feature modules
import { AgentsModule } from './agents/agents.module';
import { ApiModule } from './api/api.module';
import { DiscordModule } from './discord/discord.module';
import { FeaturesModule } from './features/features.module';

// Guards and Interceptors

// Configuration
import { configValidation } from './core/config/config.validation';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      validate: configValidation,
      envFilePath: ['.env.local', '.env'],
    }),

    // Schedule for cron jobs
    ScheduleModule.forRoot(),

    // Discord client (conditional - only if token is provided)
    ...(process.env.DISCORD_TOKEN ? [
      NecordModule.forRootAsync({
        useFactory: () => ({
          token: process.env.DISCORD_TOKEN!,
          intents: [
            GatewayIntentBits.Guilds,
            GatewayIntentBits.GuildMembers,
            GatewayIntentBits.GuildMessages,
            GatewayIntentBits.GuildPresences,
            GatewayIntentBits.DirectMessages,
            GatewayIntentBits.MessageContent,
          ],
          development: process.env.NODE_ENV === 'development' && process.env.GUILD_ID ? [process.env.GUILD_ID] : false,
        }),
      })
    ] : []),

    // Core infrastructure
    CoreModule,
    DatabaseModule,
    SecurityModule,
    // SecurityEnhancedModule, // TODO: Add when security modules are created
    // MonitoringModule, // TODO: Add when monitoring modules are created
    // CacheModule, // TODO: Add when cache modules are created
    // MultiTenancyModule, // TODO: Add when multi-tenancy modules are created

    // Feature modules
    DiscordModule,
    ApiModule,
    AgentsModule,
    FeaturesModule,
  ],
})
export class AppModule {}