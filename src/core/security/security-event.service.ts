import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export enum SecurityEventType {
  FAILED_LOGIN = 'failed_login',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  DATA_BREACH_ATTEMPT = 'data_breach_attempt',
  PRIVILEGE_ESCALATION = 'privilege_escalation',
  MALICIOUS_REQUEST = 'malicious_request',
  ACCOUNT_LOCKOUT = 'account_lockout',
  PASSWORD_BREACH = 'password_breach',
  API_ABUSE = 'api_abuse',
}

export enum SecurityEventSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  severity: SecurityEventSeverity;
  title: string;
  description: string;
  userId?: string;
  organizationId?: string;
  ipAddress?: string;
  userAgent?: string;
  endpoint?: string;
  method?: string;
  details: Record<string, any>;
  metadata: SecurityEventMetadata;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
  resolutionNotes?: string;
}

export interface SecurityEventMetadata {
  correlationId?: string;
  requestId?: string;
  sessionId?: string;
  source: string;
  version: string;
  environment: string;
}

export interface SecurityEventQuery {
  type?: SecurityEventType;
  severity?: SecurityEventSeverity;
  userId?: string;
  organizationId?: string;
  ipAddress?: string;
  startDate?: Date;
  endDate?: Date;
  resolved?: boolean;
  limit?: number;
  offset?: number;
}

export interface SecurityEventSummary {
  totalEvents: number;
  unresolvedEvents: number;
  criticalEvents: number;
  highSeverityEvents: number;
  eventsByType: Array<{ type: SecurityEventType; count: number }>;
  eventsBySeverity: Array<{ severity: SecurityEventSeverity; count: number }>;
  topIpAddresses: Array<{ ipAddress: string; count: number }>;
  recentEvents: SecurityEvent[];
}

export interface SecurityAlert {
  id: string;
  eventId: string;
  type: 'email' | 'webhook' | 'sms';
  recipient: string;
  sent: boolean;
  sentAt?: Date;
  error?: string;
}

@Injectable()
export class SecurityEventService {
  private readonly logger = new Logger(SecurityEventService.name);
  private readonly securityEvents: SecurityEvent[] = []; // In-memory storage for demo
  private readonly securityAlerts: SecurityAlert[] = [];
  private readonly maxEvents: number;

  constructor(private readonly configService: ConfigService) {
    this.maxEvents = this.configService.get<number>('SECURITY_MAX_EVENTS', 50000);
  }

  /**
   * Record a security event
   */
  async recordEvent(
    type: SecurityEventType,
    severity: SecurityEventSeverity,
    title: string,
    description: string,
    details: Record<string, any> = {},
    context?: {
      userId?: string;
      organizationId?: string;
      ipAddress?: string;
      userAgent?: string;
      endpoint?: string;
      method?: string;
      correlationId?: string;
      requestId?: string;
      sessionId?: string;
    },
  ): Promise<SecurityEvent> {
    try {
      const securityEvent: SecurityEvent = {
        id: this.generateEventId(),
        type,
        severity,
        title,
        description,
        userId: context?.userId,
        organizationId: context?.organizationId,
        ipAddress: context?.ipAddress,
        userAgent: context?.userAgent,
        endpoint: context?.endpoint,
        method: context?.method,
        details,
        metadata: {
          correlationId: context?.correlationId,
          requestId: context?.requestId,
          sessionId: context?.sessionId,
          source: 'discord-bot-energex',
          version: '1.0.0',
          environment: this.configService.get<string>('NODE_ENV', 'development'),
        },
        timestamp: new Date(),
        resolved: false,
      };

      // Store event
      this.securityEvents.push(securityEvent);

      // Maintain max events limit
      if (this.securityEvents.length > this.maxEvents) {
        this.securityEvents.shift();
      }

      // Log event
      this.logger.warn(`Security Event: ${type} - ${title}`, {
        eventId: securityEvent.id,
        severity,
        userId: context?.userId,
        ipAddress: context?.ipAddress,
        details,
      });

      // Send alerts for high severity events
      if (severity === SecurityEventSeverity.HIGH || severity === SecurityEventSeverity.CRITICAL) {
        await this.sendAlerts(securityEvent);
      }

      return securityEvent;
    } catch (error) {
      this.logger.error('Failed to record security event:', error);
      throw error;
    }
  }

  /**
   * Record failed login attempt
   */
  async recordFailedLogin(
    userId: string,
    ipAddress: string,
    userAgent?: string,
    details: Record<string, any> = {},
  ): Promise<SecurityEvent> {
    return this.recordEvent(
      SecurityEventType.FAILED_LOGIN,
      SecurityEventSeverity.MEDIUM,
      'Failed Login Attempt',
      `Failed login attempt for user ${userId}`,
      details,
      { userId, ipAddress, userAgent },
    );
  }

  /**
   * Record suspicious activity
   */
  async recordSuspiciousActivity(
    description: string,
    details: Record<string, any> = {},
    context?: {
      userId?: string;
      ipAddress?: string;
      userAgent?: string;
      endpoint?: string;
    },
  ): Promise<SecurityEvent> {
    return this.recordEvent(
      SecurityEventType.SUSPICIOUS_ACTIVITY,
      SecurityEventSeverity.HIGH,
      'Suspicious Activity Detected',
      description,
      details,
      context,
    );
  }

  /**
   * Record rate limit exceeded
   */
  async recordRateLimitExceeded(
    ipAddress: string,
    endpoint: string,
    details: Record<string, any> = {},
    context?: {
      userId?: string;
      userAgent?: string;
    },
  ): Promise<SecurityEvent> {
    return this.recordEvent(
      SecurityEventType.RATE_LIMIT_EXCEEDED,
      SecurityEventSeverity.MEDIUM,
      'Rate Limit Exceeded',
      `Rate limit exceeded for ${endpoint}`,
      details,
      { ipAddress, endpoint, ...context },
    );
  }

  /**
   * Record unauthorized access attempt
   */
  async recordUnauthorizedAccess(
    resource: string,
    details: Record<string, any> = {},
    context?: {
      userId?: string;
      organizationId?: string;
      ipAddress?: string;
      userAgent?: string;
      endpoint?: string;
    },
  ): Promise<SecurityEvent> {
    return this.recordEvent(
      SecurityEventType.UNAUTHORIZED_ACCESS,
      SecurityEventSeverity.HIGH,
      'Unauthorized Access Attempt',
      `Unauthorized access attempt to ${resource}`,
      details,
      {
        userId: context?.userId,
        organizationId: context?.organizationId,
        ipAddress: context?.ipAddress,
        userAgent: context?.userAgent,
        endpoint: context?.endpoint,
      },
    );
  }

  /**
   * Record data breach attempt
   */
  async recordDataBreachAttempt(
    description: string,
    details: Record<string, any> = {},
    context?: {
      userId?: string;
      ipAddress?: string;
      userAgent?: string;
      endpoint?: string;
    },
  ): Promise<SecurityEvent> {
    return this.recordEvent(
      SecurityEventType.DATA_BREACH_ATTEMPT,
      SecurityEventSeverity.CRITICAL,
      'Data Breach Attempt',
      description,
      details,
      context,
    );
  }

  /**
   * Query security events
   */
  async queryEvents(query: SecurityEventQuery): Promise<SecurityEvent[]> {
    try {
      let filteredEvents = [...this.securityEvents];

      // Apply filters
      if (query.type) {
        filteredEvents = filteredEvents.filter(event => event.type === query.type);
      }

      if (query.severity) {
        filteredEvents = filteredEvents.filter(event => event.severity === query.severity);
      }

      if (query.userId) {
        filteredEvents = filteredEvents.filter(event => event.userId === query.userId);
      }

      if (query.organizationId) {
        filteredEvents = filteredEvents.filter(event => event.organizationId === query.organizationId);
      }

      if (query.ipAddress) {
        filteredEvents = filteredEvents.filter(event => event.ipAddress === query.ipAddress);
      }

      if (query.startDate) {
        filteredEvents = filteredEvents.filter(event => event.timestamp >= query.startDate!);
      }

      if (query.endDate) {
        filteredEvents = filteredEvents.filter(event => event.timestamp <= query.endDate!);
      }

      if (query.resolved !== undefined) {
        filteredEvents = filteredEvents.filter(event => event.resolved === query.resolved);
      }

      // Sort by timestamp (newest first)
      filteredEvents.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      // Apply pagination
      const offset = query.offset || 0;
      const limit = query.limit || 100;
      
      return filteredEvents.slice(offset, offset + limit);
    } catch (error) {
      this.logger.error('Failed to query security events:', error);
      return [];
    }
  }

  /**
   * Get security event summary
   */
  async getSummary(query: Partial<SecurityEventQuery> = {}): Promise<SecurityEventSummary> {
    try {
      const events = await this.queryEvents({ ...query, limit: undefined, offset: undefined });

      const totalEvents = events.length;
      const unresolvedEvents = events.filter(event => !event.resolved).length;
      const criticalEvents = events.filter(event => event.severity === SecurityEventSeverity.CRITICAL).length;
      const highSeverityEvents = events.filter(event => event.severity === SecurityEventSeverity.HIGH).length;

      // Events by type
      const typeCounts = new Map<SecurityEventType, number>();
      events.forEach(event => {
        const count = typeCounts.get(event.type) || 0;
        typeCounts.set(event.type, count + 1);
      });
      const eventsByType = Array.from(typeCounts.entries())
        .map(([type, count]) => ({ type, count }))
        .sort((a, b) => b.count - a.count);

      // Events by severity
      const severityCounts = new Map<SecurityEventSeverity, number>();
      events.forEach(event => {
        const count = severityCounts.get(event.severity) || 0;
        severityCounts.set(event.severity, count + 1);
      });
      const eventsBySeverity = Array.from(severityCounts.entries())
        .map(([severity, count]) => ({ severity, count }))
        .sort((a, b) => b.count - a.count);

      // Top IP addresses
      const ipCounts = new Map<string, number>();
      events.forEach(event => {
        if (event.ipAddress) {
          const count = ipCounts.get(event.ipAddress) || 0;
          ipCounts.set(event.ipAddress, count + 1);
        }
      });
      const topIpAddresses = Array.from(ipCounts.entries())
        .map(([ipAddress, count]) => ({ ipAddress, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // Recent events
      const recentEvents = events.slice(0, 10);

      return {
        totalEvents,
        unresolvedEvents,
        criticalEvents,
        highSeverityEvents,
        eventsByType,
        eventsBySeverity,
        topIpAddresses,
        recentEvents,
      };
    } catch (error) {
      this.logger.error('Failed to get security event summary:', error);
      return {
        totalEvents: 0,
        unresolvedEvents: 0,
        criticalEvents: 0,
        highSeverityEvents: 0,
        eventsByType: [],
        eventsBySeverity: [],
        topIpAddresses: [],
        recentEvents: [],
      };
    }
  }

  /**
   * Resolve security event
   */
  async resolveEvent(
    eventId: string,
    resolvedBy: string,
    resolutionNotes?: string,
  ): Promise<SecurityEvent | null> {
    const event = this.securityEvents.find(e => e.id === eventId);
    if (!event) {
      return null;
    }

    event.resolved = true;
    event.resolvedAt = new Date();
    event.resolvedBy = resolvedBy;
    event.resolutionNotes = resolutionNotes;

    this.logger.log(`Resolved security event ${eventId}`, { resolvedBy, resolutionNotes });
    return event;
  }

  /**
   * Send alerts for security event
   */
  private async sendAlerts(event: SecurityEvent): Promise<void> {
    try {
      // In a real implementation, this would send emails, webhooks, SMS, etc.
      this.logger.warn(`SECURITY ALERT: ${event.title}`, {
        eventId: event.id,
        severity: event.severity,
        type: event.type,
        description: event.description,
      });

      // Mock alert record
      const alert: SecurityAlert = {
        id: this.generateAlertId(),
        eventId: event.id,
        type: 'email',
        recipient: '<EMAIL>',
        sent: true,
        sentAt: new Date(),
      };

      this.securityAlerts.push(alert);
    } catch (error) {
      this.logger.error('Failed to send security alerts:', error);
    }
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique alert ID
   */
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
